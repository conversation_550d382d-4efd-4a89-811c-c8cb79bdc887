package com.cryptoexchange.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cryptoexchange.entity.User;
import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserService extends IService<User> {

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    User findByEmail(String email);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    User findByPhone(String phone);

    /**
     * 根据推荐码查询用户
     * 
     * @param referralCode 推荐码
     * @return 用户信息
     */
    User findByReferralCode(String referralCode);

    /**
     * 创建用户
     * 
     * @param user 用户信息
     * @return 创建的用户
     */
    User createUser(User user);

    /**
     * 更新用户信息
     * 
     * @param user 用户信息
     * @return 更新的用户
     */
    User updateUser(User user);

    /**
     * 更新用户状态
     * 
     * @param userId 用户ID
     * @param status 状态
     */
    void updateUserStatus(Long userId, Integer status);

    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码（已加密）
     */
    void updatePassword(Long userId, String newPassword);

    /**
     * 更新交易密码
     * 
     * @param userId 用户ID
     * @param tradingPassword 交易密码（已加密）
     */
    void updateTradingPassword(Long userId, String tradingPassword);

    /**
     * 更新用户头像
     * 
     * @param userId 用户ID
     * @param avatar 头像URL
     */
    AvatarUploadResponse uploadAvatar(Long userId, MultipartFile file);

    /**
     * 更新用户昵称
     * 
     * @param userId 用户ID
     * @param nickname 昵称
     */
    void updateNickname(Long userId, String nickname);

    /**
     * 更新用户语言偏好
     * 
     * @param userId 用户ID
     * @param language 语言
     */
    void updateLanguage(Long userId, String language);

    /**
     * 更新用户时区
     * 
     * @param userId 用户ID
     * @param timezone 时区
     */
    void updateTimezone(Long userId, String timezone);

    /**
     * 启用/禁用两步验证
     * 
     * @param userId 用户ID
     * @param enabled 是否启用
     */
    void updateTwoFactorAuth(Long userId, Boolean enabled);

    /**
     * 更新KYC状态
     * 
     * @param userId 用户ID
     * @param kycStatus KYC状态
     */
    void updateKycStatus(Long userId, Integer kycStatus);

    /**
     * 更新实名信息
     * 
     * @param userId 用户ID
     * @param realName 真实姓名
     * @param idNumber 身份证号
     */
    void updateRealNameInfo(Long userId, String realName, String idNumber);

    /**
     * 更新手续费等级
     * 
     * @param userId 用户ID
     * @param feeLevel 手续费等级
     */
    void updateFeeLevel(Long userId, Integer feeLevel);

    /**
     * 更新用户交易量
     * 
     * @param userId 用户ID
     * @param volume 交易量
     */
    void updateTradingVolume(Long userId, java.math.BigDecimal volume);

    /**
     * 更新最后登录信息
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @param userAgent 用户代理
     */
    void updateLastLoginInfo(Long userId, String loginIp, String userAgent);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @return 是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 检查推荐码是否存在
     * 
     * @param referralCode 推荐码
     * @return 是否存在
     */
    boolean existsByReferralCode(String referralCode);

    /**
     * 生成唯一推荐码
     * 
     * @return 推荐码
     */
    String generateUniqueReferralCode();

    /**
     * 分页查询用户列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @param keyword 关键词
     * @param status 状态
     * @param userType 用户类型
     * @param kycStatus KYC状态
     * @return 用户列表
     */
    PageResult<User> findUsers(Integer page, Integer size, String keyword, 
                              Integer status, Integer userType, Integer kycStatus);

    /**
     * 获取用户推荐列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 推荐用户列表
     */
    PageResult<User> findReferralUsers(Long userId, Integer page, Integer size);

    /**
     * 获取用户统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    java.util.Map<String, Object> getUserStatistics(Long userId);

    /**
     * 软删除用户
     * 
     * @param userId 用户ID
     */
    void softDeleteUser(Long userId);

    /**
     * 恢复已删除用户
     * 
     * @param userId 用户ID
     */
    void restoreUser(Long userId);

    /**
     * 验证用户密码
     * 
     * @param userId 用户ID
     * @param rawPassword 原始密码
     * @return 是否匹配
     */
    boolean verifyPassword(Long userId, String rawPassword);

    /**
     * 验证交易密码
     * 
     * @param userId 用户ID
     * @param rawTradingPassword 原始交易密码
     * @return 是否匹配
     */
    boolean verifyTradingPassword(Long userId, String rawTradingPassword);
    
    // ========== Controller调用的方法 ==========
    
    /**
     * 获取用户资料
     */
    Result<UserProfileResponse> getUserProfile(Long userId);
    
    /**
     * 更新用户资料
     */
    Result<Void> updateUserProfile(Long userId, UpdateUserProfileRequest request);
    
    /**
     * 修改密码
     */
    Result<Void> changePassword(Long userId, ChangePasswordRequest request);
    
    /**
     * 设置交易密码
     */
    Result<Void> setTradingPassword(Long userId, SetTradingPasswordRequest request);
    
    /**
     * 获取安全设置
     */
    Result<SecuritySettingsResponse> getSecuritySettings(Long userId);
    
    /**
     * 获取KYC状态
     */
    Result<KycStatusResponse> getKycStatus(Long userId);
    

    
    /**
     * 获取登录历史
     */
    Result<LoginHistoryResponse> getLoginHistory(Long userId, Integer pageNum, Integer pageSize);
    
    /**
     * 获取用户等级
     */
    Result<UserLevelResponse> getUserLevel(Long userId);
    
    /**
     * 获取推荐信息
     */
    Result<ReferralInfoResponse> getReferralInfo(Long userId);
    
    /**
     * 获取推荐用户列表
     */
    Result<ReferralUserResponse> getReferralUsers(Long userId, Integer pageNum, Integer pageSize);
    
    /**
     * 获取用户权限
     */
    Result<UserPermissionsResponse> getUserPermissions(Long userId);
    
    /**
     * 获取用户资产概览
     */
    Result<UserAssetOverviewResponse> getUserAssetOverview(Long userId);
    
    /**
     * 获取用户交易概览
     */
    Result<UserTradingOverviewResponse> getUserTradingOverview(Long userId);
    
    /**
     * 签到
     */
    Result<CheckInResponse> checkIn(Long userId);
    
    /**
     * 获取签到记录
     */
    Result<CheckInRecordResponse> getCheckInRecords(Long userId, Integer pageNum, Integer pageSize);
    
    /**
     * 标记通知为已读
     */
    Result<Void> markNotificationAsRead(Long userId, Long notificationId);
    
    /**
     * 获取未读通知数量
     */
    Result<UnreadNotificationCountResponse> getUnreadNotificationCount(Long userId);
    
    /**
     * 获取用户通知列表
     */
    PageResult<UserNotificationResponse> getUserNotifications(UserNotificationQueryRequest request);
    
    /**
     * 批量标记通知为已读
     */
    Result<Void> batchMarkNotificationsAsRead(Long userId, List<Long> notificationIds);
    
    /**
     * 获取用户列表（管理员）
     */
    PageResult<AdminUserResponse> getUsers(AdminUserQueryRequest request);
    
    /**
     * 冻结用户
     */
    Result<Void> freezeUser(Long userId, String reason);
    
    /**
     * 解冻用户
     */
    Result<Void> unfreezeUser(Long userId);
    
    /**
     * 审核KYC
     */
    Result<Void> reviewKyc(Long kycId, KycReviewRequest request);
    
    /**
     * 获取用户统计（重载方法）
     */
    Result<UserStatisticsResponse> getUserStatistics(String period);
    
    /**
     * 绑定邮箱
     */
    Result<Void> bindEmail(Long userId, BindEmailRequest request);
    
    /**
     * 绑定手机号
     */
    Result<Void> bindPhone(Long userId, BindPhoneRequest request);
    
    /**
     * 设置双因子认证
     */
    TwoFactorAuthResponse setupTwoFactorAuth(Long userId, SetupTwoFactorAuthRequest request);
    
    /**
     * 验证双因子认证
     */
    Result<Void> verifyTwoFactorAuth(Long userId, String code);
    
    /**
     * 更新安全设置
     */
    Result<Void> updateSecuritySettings(Long userId, UpdateSecuritySettingsRequest request);
}