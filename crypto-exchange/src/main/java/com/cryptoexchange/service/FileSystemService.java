package com.cryptoexchange.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件系统服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class FileSystemService {

    /**
     * 创建目录
     * 
     * @param dirPath 目录路径
     * @return 是否创建成功
     */
    public boolean createDirectory(String dirPath) {
        try {
            Path path = Paths.get(dirPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                log.info("目录创建成功: {}", dirPath);
            }
            return true;
        } catch (IOException e) {
            log.error("目录创建失败: {}", dirPath, e);
            return false;
        }
    }

    /**
     * 删除文件或目录
     * 
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                if (Files.isDirectory(path)) {
                    deleteDirectoryRecursively(path);
                } else {
                    Files.delete(path);
                }
                log.info("文件删除成功: {}", filePath);
            }
            return true;
        } catch (IOException e) {
            log.error("文件删除失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectoryRecursively(Path path) throws IOException {
        Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Files.delete(file);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                Files.delete(dir);
                return FileVisitResult.CONTINUE;
            }
        });
    }

    /**
     * 复制文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 是否复制成功
     */
    public boolean copyFile(String sourcePath, String targetPath) {
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            // 确保目标目录存在
            Files.createDirectories(target.getParent());
            
            Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
            log.info("文件复制成功: {} -> {}", sourcePath, targetPath);
            return true;
        } catch (IOException e) {
            log.error("文件复制失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }

    /**
     * 移动文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 是否移动成功
     */
    public boolean moveFile(String sourcePath, String targetPath) {
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            // 确保目标目录存在
            Files.createDirectories(target.getParent());
            
            Files.move(source, target, StandardCopyOption.REPLACE_EXISTING);
            log.info("文件移动成功: {} -> {}", sourcePath, targetPath);
            return true;
        } catch (IOException e) {
            log.error("文件移动失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public boolean fileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }

    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    public long getFileSize(String filePath) {
        try {
            return Files.size(Paths.get(filePath));
        } catch (IOException e) {
            log.error("获取文件大小失败: {}", filePath, e);
            return -1;
        }
    }

    /**
     * 列出目录下的所有文件
     * 
     * @param dirPath 目录路径
     * @return 文件列表
     */
    public List<String> listFiles(String dirPath) {
        List<String> files = new ArrayList<>();
        try {
            Path path = Paths.get(dirPath);
            if (Files.exists(path) && Files.isDirectory(path)) {
                Files.list(path).forEach(file -> files.add(file.toString()));
            }
        } catch (IOException e) {
            log.error("列出文件失败: {}", dirPath, e);
        }
        return files;
    }

    /**
     * 获取可用磁盘空间
     * 
     * @param path 路径
     * @return 可用空间（字节）
     */
    public long getAvailableSpace(String path) {
        try {
            File file = new File(path);
            return file.getUsableSpace();
        } catch (Exception e) {
            log.error("获取可用空间失败: {}", path, e);
            return -1;
        }
    }

    /**
     * 获取总磁盘空间
     *
     * @param path 路径
     * @return 总空间（字节）
     */
    public long getTotalSpace(String path) {
        try {
            File file = new File(path);
            return file.getTotalSpace();
        } catch (Exception e) {
            log.error("获取总空间失败: {}", path, e);
            return -1;
        }
    }

    /**
     * 复制目录
     *
     * @param sourcePath 源目录路径
     * @param targetPath 目标目录路径
     * @return 是否复制成功
     */
    public boolean copyDirectory(String sourcePath, String targetPath) {
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);

            if (!Files.exists(source)) {
                log.error("源目录不存在: {}", sourcePath);
                return false;
            }

            Files.walkFileTree(source, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                    Path targetDir = target.resolve(source.relativize(dir));
                    Files.createDirectories(targetDir);
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    Path targetFile = target.resolve(source.relativize(file));
                    Files.copy(file, targetFile, StandardCopyOption.REPLACE_EXISTING);
                    return FileVisitResult.CONTINUE;
                }
            });

            log.info("目录复制成功: {} -> {}", sourcePath, targetPath);
            return true;
        } catch (IOException e) {
            log.error("目录复制失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }

    /**
     * 复制最近的日志文件
     *
     * @param logPath 日志目录路径
     * @param targetPath 目标目录路径
     * @param retentionDays 保留天数
     * @return 是否复制成功
     */
    public boolean copyRecentLogs(String logPath, String targetPath, int retentionDays) {
        try {
            Path logDir = Paths.get(logPath);
            Path targetDir = Paths.get(targetPath);

            if (!Files.exists(logDir)) {
                log.error("日志目录不存在: {}", logPath);
                return false;
            }

            Files.createDirectories(targetDir);

            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);

            Files.walk(logDir)
                .filter(Files::isRegularFile)
                .filter(path -> {
                    try {
                        LocalDateTime fileTime = LocalDateTime.ofInstant(
                            Files.getLastModifiedTime(path).toInstant(),
                            ZoneId.systemDefault()
                        );
                        return fileTime.isAfter(cutoffTime);
                    } catch (IOException e) {
                        log.warn("无法获取文件修改时间: {}", path, e);
                        return false;
                    }
                })
                .forEach(path -> {
                    try {
                        Path targetFile = targetDir.resolve(logDir.relativize(path));
                        Files.createDirectories(targetFile.getParent());
                        Files.copy(path, targetFile, StandardCopyOption.REPLACE_EXISTING);
                    } catch (IOException e) {
                        log.error("复制日志文件失败: {}", path, e);
                    }
                });

            log.info("复制最近日志文件成功: {} -> {}, 保留天数: {}", logPath, targetPath, retentionDays);
            return true;
        } catch (IOException e) {
            log.error("复制最近日志文件失败: {} -> {}", logPath, targetPath, e);
            return false;
        }
    }

    /**
     * 复制指定时间之后的日志文件
     *
     * @param logPath 日志目录路径
     * @param targetPath 目标目录路径
     * @param since 起始时间
     * @return 是否复制成功
     */
    public boolean copyLogsSince(String logPath, String targetPath, LocalDateTime since) {
        try {
            Path logDir = Paths.get(logPath);
            Path targetDir = Paths.get(targetPath);

            if (!Files.exists(logDir)) {
                log.error("日志目录不存在: {}", logPath);
                return false;
            }

            Files.createDirectories(targetDir);

            Files.walk(logDir)
                .filter(Files::isRegularFile)
                .filter(path -> {
                    try {
                        LocalDateTime fileTime = LocalDateTime.ofInstant(
                            Files.getLastModifiedTime(path).toInstant(),
                            ZoneId.systemDefault()
                        );
                        return fileTime.isAfter(since);
                    } catch (IOException e) {
                        log.warn("无法获取文件修改时间: {}", path, e);
                        return false;
                    }
                })
                .forEach(path -> {
                    try {
                        Path targetFile = targetDir.resolve(logDir.relativize(path));
                        Files.createDirectories(targetFile.getParent());
                        Files.copy(path, targetFile, StandardCopyOption.REPLACE_EXISTING);
                    } catch (IOException e) {
                        log.error("复制日志文件失败: {}", path, e);
                    }
                });

            log.info("复制指定时间后日志文件成功: {} -> {}, 起始时间: {}", logPath, targetPath, since);
            return true;
        } catch (IOException e) {
            log.error("复制指定时间后日志文件失败: {} -> {}", logPath, targetPath, e);
            return false;
        }
    }
}