package com.cryptoexchange.service;

import com.cryptoexchange.entity.BackupRecord;
import java.util.Map;

/**
 * 通知服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface NotificationService {

    /**
     * 发送邮件通知
     */
    void sendEmailNotification(Long userId, String subject, String content);

    /**
     * 发送短信通知
     */
    void sendSmsNotification(Long userId, String message);

    /**
     * 发送站内信
     */
    void sendInternalMessage(Long userId, String title, String content);

    /**
     * 发送推送通知
     */
    void sendPushNotification(Long userId, String title, String message, Map<String, Object> data);

    /**
     * 发送交易通知
     */
    void sendTradeNotification(Long userId, String symbol, String side, String amount, String price);

    /**
     * 发送订单状态通知
     */
    void sendOrderStatusNotification(Long userId, Long orderId, String status);

    /**
     * 发送充值通知
     */
    void sendDepositNotification(Long userId, String currency, String amount, String status);

    /**
     * 发送提现通知
     */
    void sendWithdrawNotification(Long userId, String currency, String amount, String status);

    /**
     * 发送安全警告
     */
    void sendSecurityAlert(Long userId, String alertType, String description);

    /**
     * 发送系统公告
     */
    void sendSystemAnnouncement(String title, String content, String targetUsers);

    /**
     * 发送强制平仓通知
     */
    void sendLiquidationNotification(Long userId, String symbol, String reason, String amount);

    /**
     * 发送备份失败通知
     */
    void sendBackupFailureNotification(BackupRecord backupRecord, Exception e);
}