package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 转账响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransferResponse {
    
    /**
     * 转账记录ID
     */
    private Long id;
    
    /**
     * 转出用户ID
     */
    private Long fromUserId;
    
    /**
     * 转入用户ID
     */
    private Long toUserId;
    
    /**
     * 转账货币
     */
    private String currency;
    
    /**
     * 转账金额
     */
    private BigDecimal amount;
    
    /**
     * 转账类型 (INTERNAL, EXTERNAL, SPOT_TO_FUTURES, FUTURES_TO_SPOT)
     */
    private String transferType;
    
    /**
     * 转账状态 (PENDING, COMPLETED, FAILED, CANCELLED)
     */
    private String status;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 实际转账金额
     */
    private BigDecimal actualAmount;
    
    /**
     * 转出钱包类型
     */
    private String fromWalletType;
    
    /**
     * 转入钱包类型
     */
    private String toWalletType;
    
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 转账流水号
     */
    private String transferId;
    
    /**
     * 转出前余额
     */
    private BigDecimal fromBalanceBefore;
    
    /**
     * 转出后余额
     */
    private BigDecimal fromBalanceAfter;
    
    /**
     * 转入前余额
     */
    private BigDecimal toBalanceBefore;
    
    /**
     * 转入后余额
     */
    private BigDecimal toBalanceAfter;
    
    /**
     * 审核状态
     */
    private String auditStatus;
    
    /**
     * 风险等级
     */
    private String riskLevel;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}