package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 充值请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "充值请求")
public class DepositRequest {

    @Schema(description = "币种", example = "BTC")
    @NotBlank(message = "币种不能为空")
    private String currency;

    @Schema(description = "充值金额", example = "0.001")
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "充值金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "充值地址", example = "**********************************")
    @NotBlank(message = "充值地址不能为空")
    private String address;

    @Schema(description = "交易哈希", example = "0x1234567890abcdef...")
    @NotBlank(message = "交易哈希不能为空")
    private String txHash;

    @Schema(description = "网络类型", example = "BTC")
    private String network;

    @Schema(description = "备注", example = "充值备注")
    private String memo;
}