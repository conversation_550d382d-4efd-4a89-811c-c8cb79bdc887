package com.cryptoexchange.enums;

/**
 * 备份状态枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum BackupStatus {
    
    /**
     * 等待中
     */
    PENDING("PENDING", "等待中"),
    
    /**
     * 进行中
     */
    IN_PROGRESS("IN_PROGRESS", "进行中"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 失败
     */
    FAILED("FAILED", "失败"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),
    
    /**
     * 验证中
     */
    VALIDATING("VALIDATING", "验证中"),
    
    /**
     * 验证失败
     */
    VALIDATION_FAILED("VALIDATION_FAILED", "验证失败"),
    
    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期");
    
    private final String code;
    private final String description;
    
    BackupStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 状态代码
     * @return 备份状态枚举
     */
    public static BackupStatus fromCode(String code) {
        for (BackupStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的备份状态代码: " + code);
    }
    
    /**
     * 检查是否为最终状态
     * 
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED || this == CANCELLED || this == VALIDATION_FAILED || this == EXPIRED;
    }
    
    /**
     * 检查是否为成功状态
     * 
     * @return 是否为成功状态
     */
    public boolean isSuccessStatus() {
        return this == COMPLETED;
    }
    
    /**
     * 检查是否为失败状态
     * 
     * @return 是否为失败状态
     */
    public boolean isFailureStatus() {
        return this == FAILED || this == VALIDATION_FAILED;
    }
    
    /**
     * 检查是否为进行中状态
     * 
     * @return 是否为进行中状态
     */
    public boolean isInProgressStatus() {
        return this == PENDING || this == IN_PROGRESS || this == VALIDATING;
    }
}